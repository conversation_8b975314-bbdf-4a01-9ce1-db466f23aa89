<template>
  <view class="payment-list-container">
    <!-- 头部搜索区域 -->
    <view class="search-header">
      <view class="search-box">
        <view class="search-input-wrapper">
          <input
            v-model="searchKeyword"
            placeholder="请填写订单号、合同编号"
            class="search-input"
            @input="handleSearch"
          />
          <view class="search-icon">🔍</view>
          <view v-if="searchKeyword" class="clear-icon" @click="clearSearch">✕</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="status-filter">
        <view
          v-for="(status, index) in statusOptions"
          :key="index"
          class="status-item"
          :class="{ active: selectedStatus === status.value }"
          @click="handleStatusChange(status.value)"
        >
          <text>{{ status.label }}</text>
        </view>
      </view>
    </view>

    <!-- 卡片列表 -->
    <view class="card-list">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-wrapper">
        <text class="loading-text">加载中...</text>
      </view>

      <view
        v-for="item in filteredList"
        :key="item.id"
        class="payment-card"
      >
        <view class="card-header">
          <view class="contract-info">
            <view class="contract-row">
              <text class="label-text">合同编号</text>
              <text class="contract-number" @click="copyText(item.contractNumber, '合同编号')">{{ item.contractNumber }}</text>
            </view>
            <view class="status-tag" :class="getStatusClass(item.status)">
              <text class="status-text">{{ item.status }}</text>
            </view>
          </view>
        </view>

        <view class="card-content">
          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">📋</text>
                <text class="label">订单编号</text>
              </view>
              <text class="value clickable" @click="copyText(item.orderNumber, '订单编号')">{{ item.orderNumber }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">💰</text>
                <text class="label">订单金额</text>
              </view>
              <text class="value amount">¥{{ item.orderAmount }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">💸</text>
                <text class="label">扣款金额</text>
              </view>
              <text class="value deduction">¥{{ item.deductionAmount }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">👩‍💼</text>
                <text class="label">当前阿姨</text>
              </view>
              <text class="value">{{ item.currentAuntie }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">🏪</text>
                <text class="label">归属门店</text>
              </view>
              <text class="value">{{ item.auntieStore }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">📅</text>
                <text class="label">上户天数</text>
              </view>
              <text class="value days">{{ item.workingDays }}天</text>
            </view>
          </view>

          <!-- 奖励对象信息 - 仅在已奖励状态时显示 -->
          <view v-if="item.status === '已奖励'" class="info-row">
            <view class="info-item reward-item">
              <view class="info-header">
                <text class="info-icon">🎁</text>
                <text class="label">奖励对象</text>
              </view>
              <text class="value reward">{{ item.rewardTarget }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无数据</text>
      </view>
    </view>

    <!-- 分页 -->
    <view class="pagination-wrapper" v-if="total > 0">
      <view class="pagination-info">
        <text>共 {{ total }} 条</text>
      </view>
      <view class="pagination-controls">
        <button
          class="page-btn"
          :disabled="currentPage <= 1"
          @click="handlePrevPage"
        >
          上一页
        </button>
        <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
        <button
          class="page-btn"
          :disabled="currentPage >= totalPages"
          @click="handleNextPage"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 状态选项
      statusOptions: [
        { label: '全部', value: '' },
        { label: '待确定', value: '待确定' },
        { label: '已返回', value: '已返回' },
        { label: '已奖励', value: '已奖励' }
      ],

      // 模拟数据
      paymentList: [
        {
          id: 1,
          contractNumber: 'HT202401001',
          orderNumber: 'DD202401001',
          status: '待确定',
          orderAmount: '5000.00',
          deductionAmount: '500.00',
          currentAuntie: '张阿姨',
          auntieStore: '朝阳区门店',
          workingDays: 15,
          rewardTarget: null
        },
        {
          id: 2,
          contractNumber: 'HT202401002',
          orderNumber: 'DD202401002',
          status: '已返回',
          orderAmount: '8000.00',
          deductionAmount: '800.00',
          currentAuntie: '李阿姨',
          auntieStore: '海淀区门店',
          workingDays: 30,
          rewardTarget: null
        },
        {
          id: 3,
          contractNumber: 'HT202401003',
          orderNumber: 'DD202401003',
          status: '已奖励',
          orderAmount: '6000.00',
          deductionAmount: '600.00',
          currentAuntie: '王阿姨',
          auntieStore: '西城区门店',
          workingDays: 45,
          rewardTarget: '王阿姨'
        },
        {
          id: 4,
          contractNumber: 'HT202401004',
          orderNumber: 'DD202401004',
          status: '待确定',
          orderAmount: '7200.00',
          deductionAmount: '720.00',
          currentAuntie: '刘阿姨',
          auntieStore: '丰台区门店',
          workingDays: 22,
          rewardTarget: null
        },
        {
          id: 5,
          contractNumber: 'HT202401005',
          orderNumber: 'DD202401005',
          status: '已奖励',
          orderAmount: '9500.00',
          deductionAmount: '950.00',
          currentAuntie: '陈阿姨',
          auntieStore: '海淀区门店',
          workingDays: 60,
          rewardTarget: '陈阿姨'
        }
      ]
    }
  },

  computed: {
    // 过滤后的列表
    filteredList() {
      let list = this.paymentList

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(item =>
          item.orderNumber.toLowerCase().includes(keyword) ||
          item.contractNumber.toLowerCase().includes(keyword)
        )
      }

      // 状态过滤
      if (this.selectedStatus) {
        list = list.filter(item => item.status === this.selectedStatus)
      }

      return list
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    }
  },

  onLoad() {
    this.loadData()
  },

  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        '待确定': 'status-warning',
        '已返回': 'status-info',
        '已奖励': 'status-success'
      }
      return statusMap[status] || 'status-info'
    },

    // 清空搜索
    clearSearch() {
      this.searchKeyword = ''
      this.handleSearch()
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
      this.total = this.filteredList.length
      // 这里可以调用API进行搜索
    },

    // 状态筛选处理
    handleStatusChange(status) {
      this.selectedStatus = status
      this.currentPage = 1
      this.total = this.filteredList.length
      // 这里可以调用API进行筛选
    },

    // 上一页
    handlePrevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.loadData()
      }
    },

    // 下一页
    handleNextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
        this.loadData()
      }
    },

    // 复制文本
    copyText(text, type) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: `${type}已复制`,
            icon: 'success',
            duration: 1500
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 这里调用实际的API
        // const response = await this.$http.get('/api/payment-list', {
        //   page: this.currentPage,
        //   pageSize: this.pageSize,
        //   keyword: this.searchKeyword,
        //   status: this.selectedStatus
        // })
        // this.paymentList = response.data.list
        // this.total = response.data.total

        // 模拟API调用
        setTimeout(() => {
          this.total = this.filteredList.length
          this.loading = false
        }, 500)
      } catch (error) {
        console.error('加载数据失败:', error)
        this.loading = false
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.payment-list-container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.search-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 40rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.search-box {
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.search-input {
  flex: 1;
  height: 88rpx;
  border: none;
  background: transparent;
  font-size: 28rpx;
  padding: 0 60rpx 0 20rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #667eea;
}

.clear-icon {
  position: absolute;
  right: 24rpx;
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.clear-icon:active {
  background: rgba(0, 0, 0, 0.1);
}

.status-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
  overflow-x: auto;
  padding: 10rpx 0;
}

.status-item {
  padding: 20rpx 36rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid transparent;
  white-space: nowrap;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.status-item text {
  font-size: 28rpx;
  font-weight: 500;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.loading-wrapper {
  text-align: center;
  padding: 120rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.payment-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.payment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.payment-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid rgba(102, 126, 234, 0.1);
}

.contract-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.contract-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.label-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.contract-number {
  font-weight: 600;
  font-size: 32rpx;
  color: #667eea;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.contract-number:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.98);
}

.status-tag {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-warning {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #d63031;
}

.status-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.status-success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  gap: 40rpx;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 20rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 28rpx;
}

.label {
  color: #666;
  font-size: 24rpx;
  font-weight: 500;
}

.value {
  color: #333;
  font-size: 30rpx;
  font-weight: 600;
  margin-top: 4rpx;
}

.value.clickable {
  color: #667eea;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.value.clickable:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.98);
}

.value.amount {
  color: #e17055;
  font-weight: 700;
}

.value.deduction {
  color: #fdcb6e;
  font-weight: 700;
}

.value.days {
  color: #74b9ff;
  font-weight: 700;
}

.value.reward {
  color: #00b894;
  font-weight: 700;
}

.reward-item {
  background: linear-gradient(135deg, rgba(0, 184, 148, 0.1) 0%, rgba(0, 160, 133, 0.1) 100%);
  border: 1rpx solid rgba(0, 184, 148, 0.2);
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  margin: 40rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
}

.pagination-wrapper {
  margin-top: 40rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.page-btn {
  padding: 20rpx 36rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.page-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.page-btn[disabled] {
  background: linear-gradient(135deg, #ddd 0%, #ccc 100%);
  color: #999;
  box-shadow: none;
}

.page-info {
  font-size: 24rpx;
  color: #333;
  margin: 0 20rpx;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 750rpx) {
  .payment-list-container {
    padding: 20rpx;
  }

  .search-header {
    padding: 30rpx;
  }

  .payment-card {
    padding: 30rpx;
  }

  .info-row {
    flex-direction: column;
    gap: 20rpx;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .contract-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
    width: 100%;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 20rpx;
  }

  .status-filter {
    gap: 16rpx;
  }

  .status-item {
    padding: 16rpx 28rpx;
  }

  .info-item {
    padding: 16rpx;
  }
}

/* 添加一些动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.payment-card {
  animation: fadeIn 0.5s ease-out;
}

.loading-wrapper {
  animation: fadeIn 0.3s ease-out;
}
</style>
