<template>
  <div class="payment-list-container">
    <!-- 头部搜索区域 -->
    <div class="search-header">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="请填写订单号、合同编号"
          clearable
          @input="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <!-- 状态筛选 -->
      <div class="status-filter">
        <el-radio-group v-model="selectedStatus" @change="handleStatusChange">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="待确定">待确定</el-radio-button>
          <el-radio-button label="已返回">已返回</el-radio-button>
          <el-radio-button label="已奖励">已奖励</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 卡片列表 -->
    <div class="card-list" v-loading="loading">
      <div 
        v-for="item in filteredList" 
        :key="item.id" 
        class="payment-card"
      >
        <div class="card-header">
          <div class="contract-info">
            <span class="contract-number">合同编号：{{ item.contractNumber }}</span>
            <el-tag 
              :type="getStatusType(item.status)" 
              class="status-tag"
            >
              {{ item.status }}
            </el-tag>
          </div>
        </div>
        
        <div class="card-content">
          <div class="info-row">
            <div class="info-item">
              <span class="label">订单编号：</span>
              <span class="value">{{ item.orderNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单金额：</span>
              <span class="value amount">¥{{ item.orderAmount }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="label">扣款金额：</span>
              <span class="value deduction">¥{{ item.deductionAmount }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前阿姨：</span>
              <span class="value">{{ item.currentAuntie }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <span class="label">阿姨归属门店：</span>
              <span class="value">{{ item.auntieStore }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前上户天数：</span>
              <span class="value days">{{ item.workingDays }}天</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 模拟数据
const paymentList = ref([
  {
    id: 1,
    contractNumber: 'HT202401001',
    orderNumber: 'DD202401001',
    status: '待确定',
    orderAmount: '5000.00',
    deductionAmount: '500.00',
    currentAuntie: '张阿姨',
    auntieStore: '朝阳区门店',
    workingDays: 15
  },
  {
    id: 2,
    contractNumber: 'HT202401002',
    orderNumber: 'DD202401002',
    status: '已返回',
    orderAmount: '8000.00',
    deductionAmount: '800.00',
    currentAuntie: '李阿姨',
    auntieStore: '海淀区门店',
    workingDays: 30
  },
  {
    id: 3,
    contractNumber: 'HT202401003',
    orderNumber: 'DD202401003',
    status: '已奖励',
    orderAmount: '6000.00',
    deductionAmount: '600.00',
    currentAuntie: '王阿姨',
    auntieStore: '西城区门店',
    workingDays: 45
  }
])

// 计算属性 - 过滤后的列表
const filteredList = computed(() => {
  let list = paymentList.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    list = list.filter(item => 
      item.orderNumber.toLowerCase().includes(keyword) ||
      item.contractNumber.toLowerCase().includes(keyword)
    )
  }

  // 状态过滤
  if (selectedStatus.value) {
    list = list.filter(item => item.status === selectedStatus.value)
  }

  return list
})

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '待确定': 'warning',
    '已返回': 'info',
    '已奖励': 'success'
  }
  return statusMap[status] || 'info'
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  // 这里可以调用API进行搜索
}

// 状态筛选处理
const handleStatusChange = () => {
  currentPage.value = 1
  // 这里可以调用API进行筛选
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  // 重新加载数据
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 重新加载数据
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 这里调用实际的API
    // const response = await api.getPaymentList({
    //   page: currentPage.value,
    //   pageSize: pageSize.value,
    //   keyword: searchKeyword.value,
    //   status: selectedStatus.value
    // })
    // paymentList.value = response.data.list
    // total.value = response.data.total
    
    // 模拟API调用
    setTimeout(() => {
      total.value = filteredList.value.length
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('加载数据失败:', error)
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.payment-list-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-box {
  margin-bottom: 15px;
}

.search-input {
  max-width: 400px;
}

.status-filter {
  display: flex;
  align-items: center;
}

.card-list {
  display: grid;
  gap: 16px;
}

.payment-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.payment-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.contract-number {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.status-tag {
  font-size: 12px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  color: #666;
  font-size: 14px;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.value.amount {
  color: #f56c6c;
  font-weight: 600;
}

.value.deduction {
  color: #e6a23c;
  font-weight: 600;
}

.value.days {
  color: #409eff;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-list-container {
    padding: 10px;
  }

  .search-header {
    padding: 15px;
  }

  .payment-card {
    padding: 15px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .status-filter {
    overflow-x: auto;
  }

  .search-input {
    max-width: 100%;
  }
}
</style>
