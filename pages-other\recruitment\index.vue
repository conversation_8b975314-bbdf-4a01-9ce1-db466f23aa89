<template>
	<view>
  <view class="container">
    <!-- 左侧 店铺分 -->
    <view class="card-score">
		<image class="bg-image" style="width: 165rpx; height: 145rpx;" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1750382400477a7dc523d3ba1ecc36da18a044d199be.png">
		</image>
     <view class="circle-wrapper">
        <canvas canvas-id="progressCanvas" class="canvas" />
        <view class="circle-center">
          <text class="label">店铺分</text>
          <text class="score">{{score }}</text>
        </view>
      </view>
    </view>

    <!-- 右侧 收入信息 -->
    <view class="card-data">
      <view class="data-row">
        <view class="data-block">
          <text class="label">今日预计收入(元)</text>
           <view class="value">
                      {{ incomeInt }}<text class="value2">.{{ incomeDec }}</text>
                    </view>
        </view>
        <view class="divider" />
        <view class="data-block" >
          <text class="label" style="margin-top: 3%;">有效订单量(单)</text>
            <text class="value">{{ orders }}</text>
        </view>
		<view class="label">
			>
		</view>
      </view>
    </view>
  </view>
    <view style="height: 100rpx;"></view>
	<u-button type="primary" style="width: 80%;" @click="openModal" text="饿了么"></u-button>
	
	<u-gap height="80"></u-gap>
	 <!-- 弹窗 -->
	     <uni-popup ref="popup" type="dialog">
	       <view class="modal">
	         <view class="modal-title">饿了么数据</view>
	         <view class="modal-body">
	           <view class="input-wrap">
	             <text class="input-label">店铺分：</text>
	             <input class="input" type="digit" v-model="tempScore" />
	           </view>
	           <view class="input-wrap">
	             <text class="input-label">收入(元)：</text>
	             <input class="input" type="text" v-model="tempIncome" />
	           </view>
	           <view class="input-wrap">
	             <text class="input-label">订单量：</text>
	             <input class="input" type="number" v-model="tempOrders" />
	           </view>
	         </view>
	         <view class="modal-footer">
	           <button type="default" @click="cancel">取消</button>
	           <button type="primary" @click="confirm">确定</button>
	         </view>
	       </view>
	     </uni-popup>
		 
		 
		 <view class="score-container">
		   <!-- 店铺分 -->
		 	<view class="score-item">
		 	  <text class="mtLabel">店铺分</text>
		 	  <view class="score-value-group">
		 	    <view class="score-number">
		 	      <text class="score-main">{{ mtScore }}</text>
		 	      <image class="circle-icon" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/17503984363938e5f963996bf9fddcb92a5a010be4be.png" />
		 	      <text style="margin-top: -1rpx;" class="arrow">></text>
		 	    </view>
		 	  </view>
		 	  </view>
		 
		   <!-- 今日预计收入 -->
		   <view class="score-item">
		     <text class="mtLabel">今日预计收入/元</text>
		     <view class="score-number">
		       <text class="score-main">{{ mtIncomeInt }}</text>
		 		<text style="margin-top: 8rpx;" class="score-decimal">.{{ mtIncomeDec }}</text>
		       <text style="margin-top: -1rpx;" class="arrow">></text>
		     </view>
		   </view>
		 
		   <!-- 有效订单 -->
		   <view class="score-item">
		     <text class="mtLabel">有效订单/单</text>
		     <view class="score-number">
		       <text class="score-main">{{ mtOrders }}</text>
		       <text style="margin-top: -1rpx;" class="arrow">></text>
		     </view>
		   </view>
		 </view>
		 
		 <view style="height: 100rpx;"></view>
		 <u-button type="warning" style="width: 80%;" @click="mtOpenModal" text="美团"></u-button>
		 
		 <!-- 弹窗 -->
		     <uni-popup ref="mtPopup" type="dialog">
		       <view class="modal">
		         <view class="modal-title">美团数据</view>
		         <view class="modal-body">
		           <view class="input-wrap">
		             <text class="input-label">店铺分：</text>
		             <input class="input" type="digit" v-model="mtTempScore" />
		           </view>
		           <view class="input-wrap">
		             <text class="input-label">收入(元)：</text>
		             <input class="input" type="text" v-model="mtTempIncome" />
		           </view>
		           <view class="input-wrap">
		             <text class="input-label">订单量：</text>
		             <input class="input" type="number" v-model="mtTempOrders" />
		           </view>
		         </view>
		         <view class="modal-footer">
		           <button type="default" @click="mtCancel">取消</button>
		           <button type="primary" @click="mtConfirm">确定</button>
		         </view>
		       </view>
		     </uni-popup>
		
  </view>
</template>

<script>
export default {
	 data() {
	    return {
	      score: 95.6,
	      incomeInt: 198,
	      incomeDec: '83',
	      orders: 11,
	      showModal: false,
		  mtScore: 87,
		  mtIncomeInt: 345,
		  mtIncomeDec: '44',
		  mtOrders: 29,
	      // 临时变量
	      tempScore: '',
	      tempIncome: '',
	      tempOrders: '',
		  mtTempScore: '',
		  mtTempIncome: '',
		  mtTempOrders: ''
	    };
	  },
  onReady() {
  },
  onLoad() {
  },
   methods: {
	   mtOpenModal() {
	     this.mtTempScore = this.mtScore.toString();
	     this.mtTempIncome = `${this.mtIncomeInt}.${this.mtIncomeDec}`;
	     this.mtTempOrders = this.mtOrders.toString();
	     this.$refs.mtPopup.open();
	   },
      openModal() {
        this.tempScore = this.score.toString();
        this.tempIncome = `${this.incomeInt}.${this.incomeDec}`;
        this.tempOrders = this.orders.toString();
        this.$refs.popup.open();
      },
      cancel() {
        this.$refs.popup.close();
      },
	  mtCancel() {
	    this.$refs.mtPopup.close();
	  },
      confirm() {
        this.updateScore(parseFloat(this.tempScore));
        this.updateIncome(this.tempIncome);
        this.updateOrders(parseInt(this.tempOrders));
        this.$refs.popup.close();
      },
	  mtConfirm() {
	    this.mtUpdateScore(parseFloat(this.mtTempScore));
	    this.mtUpdateIncome(this.mtTempIncome);
	    this.mtUpdateOrders(parseInt(this.mtTempOrders));
	    this.$refs.mtPopup.close();
	  },
      updateScore(newScore) {
        this.score = newScore || 0;
      },
      updateIncome(newVal) {
        const [intPart, decPart] = newVal.toString().split('.');
        this.incomeInt = parseInt(intPart || '0');
        this.incomeDec = (decPart || '00').padEnd(2, '0');
      },
      updateOrders(newOrders) {
        this.orders = newOrders || 0;
      },
      mtUpdateScore(newScore) {
        this.mtScore = newScore || 0;
      },
      mtUpdateIncome(newVal) {
        const [intPart, decPart] = newVal.toString().split('.');
        this.mtIncomeInt = parseInt(intPart || '0');
        this.mtIncomeDec = (decPart || '00').padEnd(2, '0');
      },
      mtUpdateOrders(newOrders) {
        this.mtOrders = newOrders || 0;
      }
    }
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: row;
  padding: 20rpx;
  justify-content: space-between;
}

.card-score {
  background-color: #edf3ff;
  border-radius: 24rpx;
  padding: 20rpx;
  width: 160rpx;
  height: 130rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-wrapper {
  position: relative;
  width: 160rpx;
  margin-top: 5rpx;
  height: 140rpx;
}

.canvas {
  width: 160rpx;
  height: 140rpx;
}

.circle-center {
  position: absolute;
  top: 0;
  left: 0;
  width: 160rpx;
  height: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.circle-center .label {
  font-size: 26rpx;
  color: #666666;
}

.circle-center .score {
  font-size: 48rpx;
  font-weight: 900;
  color: #333333;
  margin-top: 15rpx;
  text-shadow: 
      0.5px 0 0 currentColor,
      -0.5px 0 0 currentColor;
}

.card-data {
  flex: 1;
  margin-left: 20rpx;
  background-color: #e4ecff;
  border-radius: 24rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.data-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.bg-image {
  position: absolute;
  z-index: 0;
}

.data-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.label {
  font-size: 28rpx;
  margin-top: 10rpx;
  color: #999999;
}

.sub {
  font-size: 22rpx;
  color: #cccccc;
  margin-top: 2rpx;
}

.value {
  font-size: 50rpx;
  font-weight: 900;
  color: #0c1f39;
  margin-top: 15rpx;
  text-shadow: 
      0.5px 0 0 currentColor,
      -0.5px 0 0 currentColor;
}
.value2 {
  font-size: 35rpx;
  color: #0c1f39;
}

.divider {
  width: 2rpx;
  height: 100rpx;
  background-color: #e6e6e6;
}

/* 弹窗 */
.modal {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  width: 600rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.modal-body .input-wrap {
  margin-bottom: 20rpx;
}
.input-label {
  font-size: 28rpx;
  color: #333;
}
.input {
  border: 1px solid #ccc;
  border-radius: 8rpx;
  padding: 10rpx;
  margin-top: 10rpx;
  font-size: 28rpx;
  width: 100%;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}


.score-container {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  background-image: linear-gradient(to right, #edebec 0%, #edebec 10%, #e8f3f7 100%);
  padding: 20rpx;
}

.score-item {
  display: flex;
  flex-direction: column;
  padding: 0 20rpx;
}

.mtLabel {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.score-value-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.score-number {
  display: flex;
}

.score-main {
  font-size: 50rpx;
  font-weight: 900;
  color: #000000;
}

.score-decimal {
  font-size: 28rpx;
  font-weight: 600;
  margin-left: 4rpx;
}

.arrow {
  font-size: 28rpx;
  color: #999999;
  margin-left: 6rpx;
}

.circle-icon {
  width: 40rpx;
  height: 40rpx;
  margin-left: 8rpx;
}

</style>
