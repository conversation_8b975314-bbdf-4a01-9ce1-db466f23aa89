<template>
  <view class="payment-list-container">
    <!-- 头部搜索区域 -->
    <view class="search-header">
      <view class="search-box">
        <view class="search-input-wrapper">
          <input
            v-model="searchKeyword"
            placeholder="请填写订单号、合同编号"
            class="search-input"
            @input="handleSearch"
          />
          <view class="search-icon">🔍</view>
          <view v-if="searchKeyword" class="clear-icon" @click="clearSearch">✕</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="status-filter">
        <view
          v-for="(status, index) in statusOptions"
          :key="index"
          class="status-item"
          :class="{ active: selectedStatus === status.value }"
          @click="handleStatusChange(status.value)"
        >
          <text>{{ status.label }}</text>
        </view>
      </view>
    </view>

    <!-- 卡片列表 -->
    <view class="card-list">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-wrapper">
        <text class="loading-text">加载中...</text>
      </view>

      <view
        v-for="item in filteredList"
        :key="item.id"
        class="payment-card"
      >
        <view class="card-header">
          <view class="contract-info">
            <text class="contract-number">合同编号：{{ item.contractNumber }}</text>
            <view class="status-tag" :class="getStatusClass(item.status)">
              <text class="status-text">{{ item.status }}</text>
            </view>
          </view>
        </view>

        <view class="card-content">
          <view class="info-row">
            <view class="info-item">
              <text class="label">订单编号：</text>
              <text class="value">{{ item.orderNumber }}</text>
            </view>
            <view class="info-item">
              <text class="label">订单金额：</text>
              <text class="value amount">¥{{ item.orderAmount }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">扣款金额：</text>
              <text class="value deduction">¥{{ item.deductionAmount }}</text>
            </view>
            <view class="info-item">
              <text class="label">当前阿姨：</text>
              <text class="value">{{ item.currentAuntie }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">阿姨归属门店：</text>
              <text class="value">{{ item.auntieStore }}</text>
            </view>
            <view class="info-item">
              <text class="label">当前上户天数：</text>
              <text class="value days">{{ item.workingDays }}天</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无数据</text>
      </view>
    </view>

    <!-- 分页 -->
    <view class="pagination-wrapper" v-if="total > 0">
      <view class="pagination-info">
        <text>共 {{ total }} 条</text>
      </view>
      <view class="pagination-controls">
        <button
          class="page-btn"
          :disabled="currentPage <= 1"
          @click="handlePrevPage"
        >
          上一页
        </button>
        <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
        <button
          class="page-btn"
          :disabled="currentPage >= totalPages"
          @click="handleNextPage"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 状态选项
      statusOptions: [
        { label: '全部', value: '' },
        { label: '待确定', value: '待确定' },
        { label: '已返回', value: '已返回' },
        { label: '已奖励', value: '已奖励' }
      ],

      // 模拟数据
      paymentList: [
        {
          id: 1,
          contractNumber: 'HT202401001',
          orderNumber: 'DD202401001',
          status: '待确定',
          orderAmount: '5000.00',
          deductionAmount: '500.00',
          currentAuntie: '张阿姨',
          auntieStore: '朝阳区门店',
          workingDays: 15
        },
        {
          id: 2,
          contractNumber: 'HT202401002',
          orderNumber: 'DD202401002',
          status: '已返回',
          orderAmount: '8000.00',
          deductionAmount: '800.00',
          currentAuntie: '李阿姨',
          auntieStore: '海淀区门店',
          workingDays: 30
        },
        {
          id: 3,
          contractNumber: 'HT202401003',
          orderNumber: 'DD202401003',
          status: '已奖励',
          orderAmount: '6000.00',
          deductionAmount: '600.00',
          currentAuntie: '王阿姨',
          auntieStore: '西城区门店',
          workingDays: 45
        },
        {
          id: 4,
          contractNumber: 'HT202401004',
          orderNumber: 'DD202401004',
          status: '待确定',
          orderAmount: '7200.00',
          deductionAmount: '720.00',
          currentAuntie: '刘阿姨',
          auntieStore: '丰台区门店',
          workingDays: 22
        }
      ]
    }
  },

  computed: {
    // 过滤后的列表
    filteredList() {
      let list = this.paymentList

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        list = list.filter(item =>
          item.orderNumber.toLowerCase().includes(keyword) ||
          item.contractNumber.toLowerCase().includes(keyword)
        )
      }

      // 状态过滤
      if (this.selectedStatus) {
        list = list.filter(item => item.status === this.selectedStatus)
      }

      return list
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    }
  },

  onLoad() {
    this.loadData()
  },

  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        '待确定': 'status-warning',
        '已返回': 'status-info',
        '已奖励': 'status-success'
      }
      return statusMap[status] || 'status-info'
    },

    // 清空搜索
    clearSearch() {
      this.searchKeyword = ''
      this.handleSearch()
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
      this.total = this.filteredList.length
      // 这里可以调用API进行搜索
    },

    // 状态筛选处理
    handleStatusChange(status) {
      this.selectedStatus = status
      this.currentPage = 1
      this.total = this.filteredList.length
      // 这里可以调用API进行筛选
    },

    // 上一页
    handlePrevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.loadData()
      }
    },

    // 下一页
    handleNextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
        this.loadData()
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 这里调用实际的API
        // const response = await this.$http.get('/api/payment-list', {
        //   page: this.currentPage,
        //   pageSize: this.pageSize,
        //   keyword: this.searchKeyword,
        //   status: this.selectedStatus
        // })
        // this.paymentList = response.data.list
        // this.total = response.data.total

        // 模拟API调用
        setTimeout(() => {
          this.total = this.filteredList.length
          this.loading = false
        }, 500)
      } catch (error) {
        console.error('加载数据失败:', error)
        this.loading = false
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.payment-list-container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-header {
  background: white;
  padding: 40rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  border: none;
  background: transparent;
  font-size: 28rpx;
  padding: 0 60rpx 0 20rpx;
}

.search-icon {
  position: absolute;
  left: 20rpx;
  font-size: 32rpx;
  color: #999;
}

.clear-icon {
  position: absolute;
  right: 20rpx;
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

.status-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
  overflow-x: auto;
}

.status-item {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background: #f0f0f0;
  border: 2rpx solid transparent;
  white-space: nowrap;
}

.status-item.active {
  background: #007aff;
  color: white;
}

.status-item text {
  font-size: 28rpx;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.loading-wrapper {
  text-align: center;
  padding: 120rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.payment-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #eee;
}

.contract-number {
  font-weight: 600;
  font-size: 32rpx;
  color: #333;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.status-warning {
  background: #fff3cd;
  color: #856404;
}

.status-info {
  background: #d1ecf1;
  color: #0c5460;
}

.status-success {
  background: #d4edda;
  color: #155724;
}

.status-text {
  font-size: 24rpx;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  gap: 40rpx;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.label {
  color: #666;
  font-size: 24rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.value.amount {
  color: #f56c6c;
  font-weight: 600;
}

.value.deduction {
  color: #e6a23c;
  font-weight: 600;
}

.value.days {
  color: #409eff;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.pagination-wrapper {
  margin-top: 40rpx;
  background: white;
  padding: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info text {
  font-size: 24rpx;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.page-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  background: #007aff;
  color: white;
  border: none;
  font-size: 24rpx;
}

.page-btn[disabled] {
  background: #ccc;
  color: #999;
}

.page-info {
  font-size: 24rpx;
  color: #333;
  margin: 0 20rpx;
}

/* 移动端适配 */
@media (max-width: 750rpx) {
  .payment-list-container {
    padding: 20rpx;
  }

  .search-header {
    padding: 30rpx;
  }

  .payment-card {
    padding: 30rpx;
  }

  .info-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 20rpx;
  }
}
</style>
