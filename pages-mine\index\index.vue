<template>
	<view v-if="show">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<view class="main">
			<uni-transition mode-class="fade" :show="current == 0">
				<works v-if="current==0" :loadMore="loadMore" />
			</uni-transition>
			<uni-transition mode-class="fade" :show="current == 1">
				<rank v-if="current==1" :refresh="refresh" />
			</uni-transition>
			<uni-transition mode-class="fade" :show="current == 2">
				<inform v-if="current==2" :refresh="refresh" :loadMore="loadMore" @menuChange="menuChange" />
			</uni-transition>
			<uni-transition mode-class="fade" :show="current == 3">
				<mine v-if="current==3" :refresh="refresh" />
			</uni-transition>
		</view>

		<u-tabbar customStyle="padding-top:10rpx" :value="current" @change="name => current = name" :fixed="true"
			:placeholder="true" :safeAreaInsetBottom="true" activeColor="#1c1c76" :border="false">
			<u-tabbar-item text="首页">
				<image class="slot-icon" slot="inactive-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-home1.png">
				</image>
				<image class="slot-icon" slot="active-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-home2.png">
				</image>
			</u-tabbar-item>
			<u-tabbar-item text="排行榜">
				<image class="slot-icon" slot="inactive-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-rank.png">
				</image>
				<image class="slot-icon" slot="active-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-rank2.png">
				</image>
			</u-tabbar-item>
			<u-tabbar-item text="消息">
				<image class="slot-icon" slot="inactive-icon" :src="informIcon"></image>
				<image class="slot-icon" slot="active-icon" :src="informIcon1"></image>
			</u-tabbar-item>
			<u-tabbar-item text="我的" @click="empower()">
				<image class="slot-icon" slot="inactive-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-mine1.png">
				</image>
				<image class="slot-icon" slot="active-icon"
					src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-mine2.png">
				</image>
			</u-tabbar-item>
		</u-tabbar>
	</view>
</template>

<script>
	import works from "@/pages-mine/works/works.vue";
	import mine from "@/pages-mine/index/mine.vue";
	import inform from "@/pages-mine/inform/inform.vue";
	import rank from "@/pages-mine/ranking/index.vue";
	export default {
		components: {
			works,
			mine,
			inform,
			rank
		},
		data() {
			return {
				show: true,
				isLogin: false,
				refresh: false,
				current: 0,
				loadMore: 0,
				memberId: uni.getStorageSync('memberId'),
				employeeType: uni.getStorageSync('employeeType') || 0,
				baomuId: null,
				scrollTop: 0,
				informIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-msg1.png',
				informIcon1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-msg2.png',
				informIconList: [{
					informIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-msg1.png',
					informIcon1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/rank/icon-msg2.png',
				}, {
					informIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-message_unread.png',
					informIcon1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-message_unread2.png',
				}],
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
			}
		},
		methods: {
			menuChange(count) {
				// 改变消息通知图标
				if (count > 0) {
					this.informIcon = this.informIconList[1].informIcon
					this.informIcon1 = this.informIconList[1].informIcon1
				} else {
					this.informIcon = this.informIconList[0].informIcon
					this.informIcon1 = this.informIconList[0].informIcon1
				}
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			empower() {
				if (!this.isLogin) {
					return
				}

				// 通用-接单提醒模板&活动进展模板
				let tmpIds = [
					"GD6KOYLLn0ZlmOZLQ0gSVm8ZUC78ex98C-gVNVMk4fk",
					"ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI"
				]
				if (this.employeeType == 10 || uni.getStorageSync("roleId") == 1) {
					//取消订单
					tmpIds.push("YRSv7oviwbjl0U-fo-ghtq6OUG2Zn4u-pqd6Y9ums90")
					//接单
					tmpIds.push("U2NPysqnHSDt_1oVl1Nvbwy_vivhiRfrlHqwwEI3xEQ")
					//服务评价
					tmpIds.push("x64aI96dYKraSdcHcCTSh7sS0Bdvx8rRfzo3TU2RXK0")
				}
				// 获取订阅消息授权
				// #ifdef  MP-WEIXIN
				wx.requestSubscribeMessage({
					tmplIds: tmpIds,
					success: res => {
						console.log("用户同意进行小程序消息订阅！")
					},
					fail: res => {}
				})
				// #endif
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '家政创业，加盟开店，分佣领钱，首选家姐',
					path: '/pages/index/index',
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '家政创业，加盟开店，分佣领钱，首选家姐',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			},
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					geocode: true,
					success: res => {
						console.log("定位调用成功")
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude)
						console.log("（高精度）当前的纬度：", res.latitude, "当前的经度", res.longitude)
						uni.setStorageSync("lat", res.latitude)
						uni.setStorageSync("lng", res.longitude)
					},
					fail: err => {
						// console.log('获取当前位置失败！：' + JSON.stringify(err))
					},
				})
			},
			checkBaomu() {
				this.http({
					url: 'checkBaomuCollectByMemberId',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync("memberId")
					},
					success: res => {
						if (res.code == 0) {
							this.baomuId = res.data.baomuId
							uni.setStorageSync("baomuId", this.baomuId)
						}
					},
				})
			},
			checkEmployee() {
				this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
				this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				console.log("是否员工：", this.isEmployee, "是否保姆：", this.isBaomu)
				console.log("员工id：", uni.getStorageSync("employeeId"))
				if (this.isEmployee) {
					if (this.isBaomu) {
						if (this.baomuId == null) {
							this.checkBaomu()
						}
					}
				}
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!this.memberId) {
					uni.setStorageSync("employeeState", -1)
					this.isLogin = false
					return false
				} else {
					this.isLogin = true
					this.checkEmployee()
					return true
				}
			}
		},
		onReachBottom() {
			this.loadMore++
		},
		onShow() {
			this.refresh = !this.refresh
		},
		onLoad(options) {
			this.show = options.show ? true : false
			this.checkLogin()
			this.getLocation()
			if (this.show) {
				return
			}
			if (!this.memberId) {
				this.show = true
				return
			}
			// 登录后直接根据角色跳转到对应的工作台页面
			let merchantCode = uni.getStorageSync('merchantCode') || null
			let isBaomu = uni.getStorageSync('isBaomu') || false
			if (this.employeeType == 10) {
				return uni.reLaunch({
					url: '/pages-work/business/businessIndex'
				})
			}else if (merchantCode) {
				return uni.reLaunch({
					url: '/pages-work/index'
				})
			}  else {
				this.show = true
			}
		},
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.slot-icon {
		width: 45rpx;
		height: 45rpx;
	}
</style>